from requests import post
from typing import Optional, Dict, Any
from app.config.app_config import app_config
from app.core.outbound.social.google_auth_provider import GoogleAuthProvider
from loguru import logger


class GoogleAuthProviderImpl(GoogleAuthProvider):
    def __init__(self):
        self.client_id = app_config.GOOGLE_CLIENT_ID
        self.client_secret = app_config.GOOGLE_CLIENT_SECRET
        self.userinfo_endpoint = "https://www.googleapis.com/oauth2/v3/userinfo"

    async def fetch_user_info(self, id_token: str) -> Optional[Dict[str, Any]]:
        """
        Validate a Google OAuth token and return user information.

        Args:
            id_token: The Google OAuth token to validate

        Returns:
            Dict containing user information if token is valid, None otherwise
        """
        try:
            user_info = post(f"{app_config.GOOGLE_AUTH_BASE_URL}?id_token={id_token}")
            return user_info.json()
        except Exception as e:
            # Log the error for debugging
            logger.error(f"Error validating Google token: {str(e)}")
            return None
