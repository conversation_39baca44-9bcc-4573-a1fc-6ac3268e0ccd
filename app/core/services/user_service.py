from typing import List, Optional, Dict, Any
from uuid import UUID

from app.core.models.user import User, UserBasicInfo
from app.core.models.role import Role
from app.core.outbound.repositories.user_repository import UserRepository
from app.core.outbound.repositories.user_role_repository import UserRoleRepository
from app.core.outbound.repositories.role_repository import RoleRepository
from app.core.outbound.repositories.role_privilege_repository import RolePrivilegeRepository
from app.core.security import get_password_hash
from loguru import logger


class UserService:
    def __init__(
        self,
        user_repository: UserRepository,
        user_role_repository: UserRoleRepository,
        role_repository: RoleRepository,
        role_privilege_repository: RolePrivilegeRepository,
    ):
        self.user_repository = user_repository
        self.user_role_repository = user_role_repository
        self.role_repository = role_repository
        self.role_privilege_repository = role_privilege_repository

    async def get_users(
        self, tenant_id: UUID, filters: Optional[Dict[str, Any]] = None, page: int = 1, page_size: int = 10
    ) -> List[User]:
        """Get all users with optional filters and pagination."""
        offset = (page - 1) * page_size if page > 1 else 0
        return await self.user_repository.get_users(tenant_id, filters, offset, page_size)

    async def create_user(self, tenant_id: UUID, user_data: Dict[str, Any]) -> User:
        """Create a new user."""
        user = User(
            tenant_id=tenant_id,
            organization_id=user_data["organization_id"],
            email=user_data["email"],
            first_name=user_data["first_name"],
            last_name=user_data["last_name"],
            active=user_data.get("active", True),
            role_ids=user_data.get("role_ids", []),
        )

        if user_data.get("password"):
            user.password_hash = get_password_hash(user_data["password"])

        return await self.user_repository.save(user)

    async def get_user_by_id(self, tenant_id: UUID, user_id: UUID) -> Optional[User]:
        """Get a user by their ID."""
        return await self.user_repository.get_user_by_id(tenant_id, user_id)

    async def update_user(self, tenant_id: UUID, user_id: UUID, user_data: Dict[str, Any]) -> User:
        """Update an existing user."""
        user = await self.user_repository.get_user_by_id(tenant_id, user_id)
        if not user:
            raise ValueError(f"User with ID {user_id} not found")

        # Update user fields
        for key, value in user_data.items():
            if key == "password":
                setattr(user, "password_hash", get_password_hash(value))
            elif hasattr(user, key) and key not in ["id", "created_at"]:
                setattr(user, key, value)
        return await self.user_repository.save(user)

    async def delete_user(self, user_id: UUID) -> None:
        """Delete a user."""
        user = await self.user_repository.get_user_by_id(user_id)
        if not user:
            raise ValueError(f"User with ID {user_id} not found")

        user.active = False
        await self.user_repository.save(user)

    async def get_user_by_email(self, email: str) -> Optional[User]:
        """Get a user by their email."""
        return await self.user_repository.get_user_by_email(email)

    async def assign_roles(self, tenant_id: UUID, user_id: UUID, role_ids: List[UUID]) -> None:
        """Assign roles to a user."""
        user = await self.user_repository.get_user_by_id(tenant_id, user_id)
        if not user:
            raise ValueError(f"User with ID {user_id} not found")

        roles = await self.role_repository.get_roles_by_ids(tenant_id, role_ids)
        new_role_ids = set(role.id for role in roles)

        await self.user_role_repository.assign_roles_to_user(tenant_id, user_id, new_role_ids)

    async def remove_roles(self, user_id: UUID, role_ids: List[UUID]) -> User:
        """Remove roles from a user."""
        user = await self.user_repository.get_user_by_id(user_id)
        if not user:
            raise ValueError(f"User with ID {user_id} not found")

        user.role_ids = [rid for rid in user.role_ids if rid not in role_ids]
        user = await self.user_repository.save(user)

        for role_id in role_ids:
            await self.user_role_repository.delete(user_id, role_id)
        return user

    async def get_user_roles(self, tenant_id: UUID, user_id: UUID) -> List[Role]:
        """Get all roles assigned to a user"""

        user_roles = await self.user_role_repository.get_user_roles(tenant_id, user_id)
        if not user_roles:
            return []
        role_privileges = await self.role_privilege_repository.get_roles_and_privileges(
            tenant_id, [user_role.role_id for user_role in user_roles]
        )

        if not role_privileges:
            raise ValueError(f"Roles not found")
        roles = [role_privileges[0][0]]
        for role_privilege in role_privileges:
            role = role_privilege[0]
            role.privileges = role_privilege[1]
            roles.append(role)
        return roles

    async def get_users_basic_info(self, tenant_id: UUID) -> List[UserBasicInfo]:
        """Get all users by their IDs"""
        users = self.user_repository.get_users(tenant_id)
        return [
           UserBasicInfo(
                id=user.id,
                tenant_id=user.tenant_id,
                organization_id=user.organization_id,
                email=user.email,
                name=user.first_name + " " + user.last_name,
            )
            for user in users
        ]
