from typing import Op<PERSON>, Dict, Any
from uuid import UUID
from datetime import datetime, UTC

from app.core.models.user import User
from app.core.models.social_auth_provider import SocialAuthProvider
from app.core.services.user_service import UserService
from app.core.security import (
    verify_password,
    get_password_hash,
    create_access_token,
    create_refresh_token,
    verify_token,
    verify_supabase_token
)
from app.config.app_config import app_config
from app.core.outbound.social.google_auth_provider import GoogleAuthProvider


class AuthService:
    def __init__(self, user_service: UserService, google_auth_provider: GoogleAuthProvider):
        self.user_service = user_service
        self.google_auth_provider = google_auth_provider

    async def login(self, email: str, password: str) -> Dict[str, Any]:
        """Authenticate user with email and password"""
        user = await self.user_service.get_user_by_email(email)
        if not user or not user.active:
            raise ValueError("User is not active")

        # Verify password hash
        if not verify_password(password, user.password_hash):
            raise ValueError("Invalid email or password")

        # Update last login
        user.last_login = datetime.now(UTC)
        await self.user_service.update_user(user.tenant_id, user.id, {"last_login": user.last_login})

        # Generate tokens
        token_data = {
            "sub": str(user.id),
            "tenant_id": str(user.tenant_id),
            "organization_id": str(user.organization_id),
        }
        access_token = create_access_token(token_data)
        refresh_token = create_refresh_token(token_data)

        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer",
            "name": user.first_name + " " + user.last_name,
            "email": user.email,
            "user_id": user.id,
            "tenant_id": user.tenant_id,
            "organization_id": user.organization_id,
            "tenant_region_url": "https://" + str(user.tenant_id) + ".mstack.co",
        }

    async def social_login(self, provider: str, token: str) -> Dict[str, Any]:
        """Authenticate user using social provider"""
        # Get the appropriate auth provider
        social_user = await self.__retrieve_provider_user(provider, token)
        if social_user is None:
            raise ValueError("Invalid social token")

        # Get or create user
        user_email = social_user.get("email")
        user = await self.user_service.get_user_by_email(user_email)
        if not user:
            raise ValueError("User not found")
        
        if not user.active:
            raise ValueError("User is not active")

        # Update last login
        user.last_login = datetime.now(UTC)
        await self.user_service.update_user(user.tenant_id, user.id, {"last_login": user.last_login})

        # Generate tokens
        token_data = {
            "sub": str(user.id),
            "tenant_id": str(user.tenant_id),
            "organization_id": str(user.organization_id),
        }
        access_token = create_access_token(token_data)
        refresh_token = create_refresh_token(token_data)

        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer",
            "name": user.first_name + " " + user.last_name,
            "email": user.email,
            "user_id": user.id,
            "tenant_id": user.tenant_id,
            "organization_id": user.organization_id,
            "tenant_region_url": "https://" + str(user.tenant_id) + ".mstack.co",
        }

    async def refresh_token(self, refresh_token: str) -> Dict[str, Any]:
        """Refresh access token using refresh token"""
        # Verify the refresh token
        payload = verify_token(refresh_token)
        if not payload or payload.get("type") != "refresh":
            raise ValueError("Invalid refresh token")

        # Create new access token
        token_data = {
            "sub": payload["sub"],
            "tenant_id": payload.get("tenant_id"),
            "organization_id": payload.get("organization_id"),
        }
        access_token = create_access_token(token_data)

        return {"access_token": access_token, "token_type": "bearer"}

    async def update_password(self, token: str, new_password: str) -> None:
        """Update user's password using reset token"""
        # Verify the reset token
        payload = verify_token(token)
        if not payload:
            raise ValueError("Invalid reset token")

        # Get user and update password
        user_id = UUID(payload["sub"])
        tenant_id = UUID(payload["tenant_id"])
        user = await self.user_service.get_user_by_id(tenant_id, user_id)
        if not user:
            raise ValueError("User not found")

        # Update password
        password_hash = get_password_hash(new_password)
        await self.user_service.update_user(tenant_id, user_id, {"password_hash": password_hash})

    async def validate_token(self, token: str) -> Dict[str, Any]:
        """Validate JWT token"""
        payload = verify_token(token)
        if not payload:
            raise ValueError("Invalid token")

        if payload.get("exp") < datetime.now(UTC).timestamp():
            raise ValueError("Token has expired")

        return payload
    
    async def is_supabase_token(self, token: str) -> bool:
        """Check if the token is a Supabase JWT token"""
        payload = verify_supabase_token(token)
        if not payload:
            raise ValueError("Invalid token")
        issuer = payload.get("iss", "")
        if issuer.startswith("https://") and ".supabase.co/auth/v1" in issuer:
            return True
        return False

    async def validate_supabase_auth_token(self, token: str) -> Dict[str, Any]:
        """Validate Supabase JWT token"""
        payload = verify_supabase_token(token)
        if not payload:
            raise ValueError("Invalid token")
        
        if payload.get("exp") < datetime.now(UTC).timestamp():
            raise ValueError("Token has expired")
        
        return payload
        
    async def authorize(self, user_id: UUID, resource: str, action: str) -> bool:
        """Check if user has permission for resource/action"""
        # TODO: Implement proper authorization logic
        # For now, just return True for testing
        return True

    async def __retrieve_provider_user(self, provider: str, token: str) -> Optional[Dict[str, Any]]:
        """Get the appropriate social auth provider"""
        if provider == "google":
            return await self.google_auth_provider.fetch_user_info(token)
        return None
