# app/main.py
from fastapi import FastAP<PERSON>, Request, HTTPException
from fastapi.responses import JSONResponse
from loguru import logger
from app.middleware import logging_middleware, auth_middleware
from app.middleware.response_headers import ResponseHeadersMiddleware
from app.config.app_config import app_config
from app.monitoring.metrics import metrics_middleware
from app.monitoring.health import health_router
from app.monitoring.metrics import metrics_router
from app.config.logging_config import setup_logging
from app.common.request_context import user_id_ctx, tenant_id_ctx
from app.common.global_exception_handler import http_exception_handler, global_exception_handler

# Import all route modules
from app.api.routes import (
    auth_router,
    tenant_router,
    organization_router,
    user_router,
    role_router,
    privilege_router,
    region_router,
    cell_router,
    legal_entity_router,
)


setup_logging(log_level=app_config.LOGGING_LEVEL)

# Combine root path with docs URL
root_path = f"/{app_config.BASE_PATH}"
docs_url = "/docs"
redoc_url = "/redoc"

app = FastAPI(
    title=app_config.PROJECT_NAME,
    description="API Documentation",
    root_path=root_path,
    docs_url=docs_url,
    redoc_url=redoc_url,
    version="1.0.0",
)

# Register exception handlers
app.add_exception_handler(HTTPException, http_exception_handler)
app.add_exception_handler(Exception, global_exception_handler)

# Add middlewares
app.middleware("http")(logging_middleware)
# app.middleware("http")(auth_middleware)
app.add_middleware(ResponseHeadersMiddleware)

# Include all routers
app.include_router(auth_router, prefix="/api/v1", tags=["Authentication"])
app.include_router(tenant_router, prefix="/api/v1", tags=["Tenant Management"])
app.include_router(organization_router, prefix="/api/v1", tags=["Organization Management"])
app.include_router(user_router, prefix="/api/v1", tags=["User Management"])
app.include_router(role_router, prefix="/api/v1", tags=["Role Management"])
app.include_router(privilege_router, prefix="/api/v1", tags=["Privilege Management"])
app.include_router(region_router, prefix="/api/v1", tags=["Region Management"])
app.include_router(cell_router, prefix="/api/v1", tags=["Cell Management"])
app.include_router(legal_entity_router, prefix="/api/v1", tags=["Legal Entity Management"])

# Metrics middleware
app.middleware("http")(metrics_middleware)

# Include monitoring routers
app.include_router(metrics_router, tags=["Monitoring"])
app.include_router(health_router)
