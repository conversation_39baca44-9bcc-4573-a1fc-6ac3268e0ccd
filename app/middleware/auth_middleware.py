from fastapi import Request, Response
from fastapi.responses import JSONResponse
from app.common.request_context import (
    user_id_ctx,
    tenant_id_ctx,
    organization_id_ctx,
    user_roles_ctx,
    user_privileges_ctx,
)
from app.core.security import verify_token
from app.api.schemas.common import ErrorResponse, ErrorDetails, ErrorCode
from app.api.dependencies import get_user_service
from app.core.services.user_service import UserService
from loguru import logger
from app.config.db import get_db
from contextlib import asynccontextmanager

NO_AUTH_PATHS = [
    "/api/v1/auth/login",
    "/api/v1/auth/refresh",
    "/t360/docs",
    "/docs",
    "/t360/openapi.json",
    "/t360/redoc",
    "/t360/favicon.ico",
    "/t360/static",
    "/t360/static/css",
    "/t360/static/js",
    "/t360/api/v1/auth/login",
    "/t360/api/v1/auth/refresh",
    "/t360/api/v1/auth/social/google/login",
    "/t360/api/v1/auth/validate-token",
    "/t360/api/v1/auth/password/update",
    "/t360/metrics",
    "/t360/health",
    "/t360/health/live",
    "/t360/health/ready",
    "/t360/api/v1/users/me",
]


def is_no_auth_path(path: str) -> bool:
    return path in NO_AUTH_PATHS


@asynccontextmanager
async def resolve_dependency(dependency_callable, *args, **kwargs):
    gen = dependency_callable(*args, **kwargs)
    value = await gen.__anext__()
    try:
        yield value
    finally:
        await gen.aclose()


async def auth_middleware(request: Request, call_next):
    """Middleware to handle authentication and set request context."""
    if is_no_auth_path(request.url.path):
        return await call_next(request)

    try:
        token = request.headers.get("Authorization")
        if not token:
            return JSONResponse(
                status_code=401,
                content=ErrorResponse(
                    success=False,
                    error=ErrorDetails(
                        code=ErrorCode.UNAUTHORIZED,
                        message="Unauthorized",
                        details={"reason": "Missing Authorization header"},
                    ),
                ).model_dump(),
            )

        # validate the token split the token into two parts
        token = token.split(" ")[1]
        payload = verify_token(token)
        if not payload:
            return JSONResponse(
                status_code=401,
                content=ErrorResponse(
                    success=False,
                    error=ErrorDetails(
                        code=ErrorCode.UNAUTHORIZED,
                        message="Unauthorized",
                        details={"reason": "Invalid or expired token"},
                    ),
                ).model_dump(),
            )

        user_id = payload.get("sub")
        tenant_id = payload.get("tenant_id")
        organization_id = payload.get("organization_id")

        user_roles = set()
        user_privileges = set()
        # Get user service and fetch user details
        async with resolve_dependency(get_db) as db:
            user_service = await get_user_service(db)
            user = await user_service.get_user_by_id(tenant_id, user_id)
            roles = await user_service.get_user_roles(tenant_id, user_id)
            for role in roles:
                user_roles.add(role.name)
                for privilege in role.privileges:
                    user_privileges.add(privilege.name)

        if not user:
            return JSONResponse(
                status_code=401,
                content=ErrorResponse(
                    success=False,
                    error=ErrorDetails(
                        code=ErrorCode.UNAUTHORIZED,
                        message="Unauthorized",
                        details={"reason": "User not found"},
                    ),
                ).model_dump(),
            )

        # Set context variables
        user_id_ctx.set(user_id)
        tenant_id_ctx.set(tenant_id)
        organization_id_ctx.set(organization_id)
        user_roles_ctx.set(user_roles)
        user_privileges_ctx.set(user_privileges)

        # Continue with the request
        response = await call_next(request)
        return response

    except Exception as e:
        return JSONResponse(
            status_code=500,
            content=ErrorResponse(
                success=False,
                error=ErrorDetails(
                    code=ErrorCode.INTERNAL_ERROR,
                    message="Internal server error",
                    details={"error": str(e)},
                ),
            ).model_dump(),
        )
    finally:
        # Clean up context
        # user_id_ctx.reset()
        # tenant_id_ctx.reset()
        # organization_id_ctx.reset()
        pass
