# app/core/config.py
import os
from dotenv import load_dotenv
from pydantic_settings import BaseSettings
from typing import Dict, Any, Optional
from app.config.vault_client import vault_client
from loguru import logger

# Load default environment variables
load_dotenv("default.env")

# Load root environment variables
root_env_file = ".env"
load_dotenv(root_env_file)

# Load environment variables
env_file = f".env.{os.getenv('ENV', 'dev')}"
load_dotenv(env_file)


class AppConfig(BaseSettings):
    ENV: str
    PROJECT_NAME: str
    LOGGING_LEVEL: str
    BASE_PATH: str
    TZ: str

    # Vault Configuration
    VAULT_ADDR: Optional[str] = None
    VAULT_TOKEN: Optional[str] = None
    VAULT_PROJECT: Optional[str] = None

    # Google Social Config
    GOOGLE_CLIENT_ID: str
    GOOGLE_CLIENT_SECRET: str
    GOOGLE_REDIRECT_URI: str = "http://localhost:8000/auth/google/callback"

    # Security Config
    ALGORITHM: str = "RS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7

    # RSA Keys
    RSA_PRIVATE_KEY: str
    RSA_PUBLIC_KEY: str

    # Database Settings
    DATABASE_URL: str = "postgresql+asyncpg://u_tenant360:3i9HUdy5nQrO75gdrFs6@localhost:5434/tenant360"

    DEFAULT_TENANT_ID: str = "dbec1474-c817-459c-8ba0-8f2d65304f18"
    PROCURESTACK_SUPABASE_JWT_SECRET: str = "Vl/2Bo3sD41wvnnjgsWGmtFxRw3yv7AbhbyH0C5FYhAag7D0ph+5LARBJK+2lcW94TXlG4OM00x3AbKssq6HaA=="
    SALESSTACK_SUPABASE_JWT_SECRET: str = "Zk658MwmNH+pK0nxSF+vWgntF0YX9cHopHueglaaGQkKl9HsARXWXUFPez4CW7q2PRp8KECFhxcOiCVOzxtLAg=="
    GOOGLE_AUTH_BASE_URL: str = os.getenv('GOOGLE_AUTH_BASE_URL')

    # # Email Settings
    # SMTP_HOST: str
    # SMTP_PORT: int
    # SMTP_USER: str
    # SMTP_PASSWORD: str
    # SMTP_FROM_EMAIL: str

    # Load secrets from Vault
    def load_secrets(self):
        """Fetch secrets from Vault and set database credentials."""
        # Skip loading secrets if Vault is not configured
        if not self.VAULT_ADDR or not self.VAULT_TOKEN:
            logger.warning("Skipping Vault secrets - VAULT_ADDR or VAULT_TOKEN not set")
            return
        try:
            vault_secret_path: str = f"{os.getenv('VAULT_PROJECT', self.PROJECT_NAME)}/{self.ENV}"
            logger.info("Loading secrets from Vault: " + vault_secret_path)
            app_secrets: Dict[str, Any] = vault_client.get_secret(vault_secret_path)
            self.DATABASE_URL = app_secrets.get("database_url")
            # .. all other configs
        except Exception as e:
            raise RuntimeError(f"Failed to load secrets from Vault: {e}")

    class Config:
        env_file = ".env"
        case_sensitive = True


# Initialize settings
app_config = AppConfig()

# Load secrets from Vault
# app_config.load_secrets()
