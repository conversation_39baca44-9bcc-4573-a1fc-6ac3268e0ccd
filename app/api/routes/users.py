from typing import Any, Dict, List, Optional, Coroutine
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, Header, Request, Response

from app.api.dependencies import get_user_service, get_auth_service
from app.api.schemas.common import ErrorCode, ErrorResponse, SuccessResponse
from app.api.schemas.user import (
    GetUserResponse,
    ListUsersResponse,
    UserCreate,
    UserResponse,
    UserRoleAssign,
    UserUpdate, UserBasicInfoResponse,
)
from app.core.services.user_service import UserService
from app.api.schemas.role import RoleResponse
from loguru import logger
from app.core.services.auth_service import AuthService
from app.api.decorators.auth_decorators import require_privileges

router = APIRouter(prefix="", tags=["User Management"])


@router.get(
    "/tenants/{tenant_id}/users/{user_id}",
    response_model=GetUserResponse,
    responses={404: {"model": ErrorResponse}, 500: {"model": ErrorResponse}},
)
@require_privileges(["READ_USER"], validate_tenant=True)
async def get_user(
    tenant_id: UUID,
    user_id: UUID,
    user_service: UserService = Depends(get_user_service),
) -> GetUserResponse:
    """Get user details by ID"""
    try:
        user = await user_service.get_user_by_id(tenant_id, user_id)
        if not user:
            raise HTTPException(
                status_code=404,
                detail={
                    "code": ErrorCode.NOT_FOUND,
                    "message": "User not found",
                    "details": {"user_id": str(user_id)},
                },
            )
        return GetUserResponse(
            success=True,
            data=UserResponse(
                id=user.id,
                tenant_id=user.tenant_id,
                organization_id=user.organization_id,
                email=user.email,
                first_name=user.first_name,
                last_name=user.last_name,
                active=user.active,
                created_at=user.created_at,
                updated_at=user.updated_at,
                role_ids=user.role_ids,
            ),
            message="User fetched successfully",
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "code": ErrorCode.INTERNAL_ERROR,
                "message": "Failed to fetch user",
                "details": {"error": str(e)},
            },
        )


@router.get("/tenants/{tenant_id}/users", response_model=ListUsersResponse, responses={500: {"model": ErrorResponse}})
@require_privileges(["READ_USER"], validate_tenant=True)
async def get_users(
    tenant_id: UUID,
    email: Optional[str] = Query(None, description="Filter by email"),
    first_name: Optional[str] = Query(None, description="Filter by first name"),
    last_name: Optional[str] = Query(None, description="Filter by last name"),
    organization_id: Optional[UUID] = Query(None, description="Filter by organization ID"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=100, description="Number of items per page"),
    user_service: UserService = Depends(get_user_service),
) -> ListUsersResponse:
    """List all users for a tenant with optional filters"""
    try:
        filters = {}
        if email:
            filters["email"] = email
        if first_name:
            filters["first_name"] = first_name
        if last_name:
            filters["last_name"] = last_name
        if organization_id:
            filters["organization_id"] = organization_id

        users = await user_service.get_users(tenant_id, filters, page, page_size)
        return ListUsersResponse(
            success=True,
            data=[
                UserResponse(
                    id=user.id,
                    tenant_id=user.tenant_id,
                    organization_id=user.organization_id,
                    email=user.email,
                    first_name=user.first_name,
                    last_name=user.last_name,
                    active=user.active,
                    created_at=user.created_at,
                    updated_at=user.updated_at,
                    role_ids=user.role_ids,
                )
                for user in users
            ],
            message=f"Successfully fetched {len(users)} users",
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "code": ErrorCode.INTERNAL_ERROR,
                "message": "Failed to fetch users",
                "details": {"error": str(e)},
            },
        )


@router.post(
    "/tenants/{tenant_id}/users",
    response_model=GetUserResponse,
    responses={400: {"model": ErrorResponse}, 500: {"model": ErrorResponse}},
)
@require_privileges(["CREATE_USER"], validate_tenant=True)
async def create_user(
    tenant_id: UUID,
    user_data: UserCreate,
    user_service: UserService = Depends(get_user_service),
    response: Response = None,
) -> GetUserResponse:
    """Create user in tenant"""
    try:
        user = await user_service.create_user(tenant_id, user_data.model_dump())

        # Method 2: Set headers using Response object with status code
        if response:
            response.status_code = 201  # Created
            response.headers["Location"] = f"/tenants/{tenant_id}/users/{user.id}"
            response.headers["X-Resource-ID"] = str(user.id)

        return GetUserResponse(
            success=True,
            data=UserResponse(
                id=user.id,
                tenant_id=user.tenant_id,
                organization_id=user.organization_id,
                email=user.email,
                first_name=user.first_name,
                last_name=user.last_name,
                active=user.active,
                created_at=user.created_at,
                updated_at=user.updated_at,
                role_ids=user.role_ids,
            ),
            message="User created successfully",
        )
    except ValueError as e:
        raise HTTPException(
            status_code=400,
            detail={
                "code": ErrorCode.VALIDATION_ERROR,
                "message": str(e),
                "details": {},
            },
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "code": ErrorCode.INTERNAL_ERROR,
                "message": "Failed to create user",
                "details": {"error": str(e)},
            },
        )


@router.put(
    "/tenants/{tenant_id}/users/{id}",
    response_model=GetUserResponse,
    responses={400: {"model": ErrorResponse}, 404: {"model": ErrorResponse}, 500: {"model": ErrorResponse}},
)
@require_privileges(["UPDATE_USER"], validate_tenant=True)
async def update_user(
    tenant_id: UUID,
    id: UUID,
    user_data: UserUpdate,
    user_service: UserService = Depends(get_user_service),
) -> GetUserResponse:
    """Update user in tenant"""
    try:
        # Convert to dict and filter out None values
        update_data = {k: v for k, v in user_data.model_dump().items() if v is not None}
        user = await user_service.update_user(tenant_id, id, update_data)
        return GetUserResponse(
            success=True,
            data=UserResponse(
                id=user.id,
                tenant_id=user.tenant_id,
                organization_id=user.organization_id,
                email=user.email,
                first_name=user.first_name,
                last_name=user.last_name,
                active=user.active,
                created_at=user.created_at,
                updated_at=user.updated_at,
                role_ids=user.role_ids,
            ),
            message="User updated successfully",
        )
    except ValueError as e:
        if "not found" in str(e).lower():
            raise HTTPException(
                status_code=404,
                detail={"code": ErrorCode.NOT_FOUND, "message": str(e), "details": {"user_id": str(id)}},
            )
        else:
            raise HTTPException(
                status_code=400, detail={"code": ErrorCode.VALIDATION_ERROR, "message": str(e), "details": {}}
            )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={"code": ErrorCode.INTERNAL_ERROR, "message": "Failed to update user", "details": {"error": str(e)}},
        )


@router.get(
    "/tenants/{tenant_id}/users/{id}/roles",
    response_model=SuccessResponse[List[RoleResponse]],
    responses={404: {"model": ErrorResponse}, 500: {"model": ErrorResponse}},
)
@require_privileges(["READ_USER"], validate_tenant=True)
async def get_user_roles(
    tenant_id: UUID, id: UUID, user_service: UserService = Depends(get_user_service)
) -> Dict[str, Any]:
    """Get user roles in tenant"""
    try:
        roles = await user_service.get_user_roles(tenant_id, id)
        return SuccessResponse(success=True, data=roles, message="Successfully fetched user roles")
    except ValueError as e:
        raise HTTPException(
            status_code=404,
            detail={
                "code": ErrorCode.NOT_FOUND,
                "message": str(e),
                "details": {"user_id": str(id)},
            },
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "code": ErrorCode.INTERNAL_ERROR,
                "message": "Failed to fetch user roles",
                "details": {"error": str(e)},
            },
        )


@router.post(
    "/tenants/{tenant_id}/users/{id}/roles",
    response_model=SuccessResponse[Dict[str, Any]],
    responses={
        400: {"model": ErrorResponse},
        404: {"model": ErrorResponse},
        500: {"model": ErrorResponse},
    },
)
@require_privileges(["ASSIGN_USER_ROLE"], validate_tenant=True)
async def assign_user_roles(
    tenant_id: UUID,
    id: UUID,
    role_data: UserRoleAssign,
    user_service: UserService = Depends(get_user_service),
) -> Dict[str, Any]:
    """Assign role to user in tenant"""
    try:
        await user_service.assign_roles(tenant_id, id, role_data.role_ids)
        return SuccessResponse(success=True, data={}, message="Successfully assigned roles to user")
    except ValueError as e:
        if "not found" in str(e).lower():
            raise HTTPException(
                status_code=404,
                detail={
                    "code": ErrorCode.NOT_FOUND,
                    "message": str(e),
                    "details": {"user_id": str(id)},
                },
            )
        else:
            raise HTTPException(
                status_code=400,
                detail={
                    "code": ErrorCode.VALIDATION_ERROR,
                    "message": str(e),
                    "details": {},
                },
            )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "code": ErrorCode.INTERNAL_ERROR,
                "message": "Failed to assign roles to user",
                "details": {"error": str(e)},
            },
        )


@router.delete(
    "/tenants/{tenant_id}/users/{id}/roles/{role_id}",
    response_model=SuccessResponse[Dict[str, Any]],
    responses={
        404: {"model": ErrorResponse},
        500: {"model": ErrorResponse},
    },
)
@require_privileges(["UNASSIGN_USER_ROLE"], validate_tenant=True)
async def remove_user_role(
    tenant_id: UUID,
    id: UUID,
    role_id: UUID,
    user_service: UserService = Depends(get_user_service),
) -> Dict[str, Any]:
    """Remove role from user in tenant"""
    try:
        await user_service.remove_roles(tenant_id, id, [role_id])
        return SuccessResponse(success=True, data={}, message="Successfully removed role from user")
    except ValueError as e:
        raise HTTPException(
            status_code=404,
            detail={
                "code": ErrorCode.NOT_FOUND,
                "message": str(e),
                "details": {"user_id": str(id), "role_id": str(role_id)},
            },
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "code": ErrorCode.INTERNAL_ERROR,
                "message": "Failed to remove role from user",
                "details": {"error": str(e)},
            },
        )


@router.get("/me", response_model=UserResponse)
async def get_current_user(
    request: Request,
    user_service: UserService = Depends(get_user_service),
    auth_service: AuthService = Depends(get_auth_service),
) -> Dict[str, Any]:
    """Get current user profile in tenant"""
    token = request.headers.get("Authorization")
    if not token:
        raise HTTPException(
            status_code=401,
            detail={"code": ErrorCode.UNAUTHORIZED, "message": "Token is required"},
        )

    token_data = await auth_service.validate_token(token)
    tenant_id = token_data.get("tenant_id")
    user_id = token_data.get("sub")

    user = await user_service.get_user_by_id(tenant_id, user_id)
    roles = await user_service.get_user_roles(tenant_id, user_id)
    return UserResponse(
        id=user.id,
        tenant_id=user.tenant_id,
        organization_id=user.organization_id,
        email=user.email,
        first_name=user.first_name,
        last_name=user.last_name,
        active=user.active,
        created_at=user.created_at,
        updated_at=user.updated_at,
        roles=roles,
    )


@router.put("/me", response_model=UserResponse)
async def update_current_user(
    user_data: UserUpdate,
    user_service: UserService = Depends(get_user_service),
) -> Dict[str, Any]:
    """Update current user profile in tenant"""
    # return await user_service.update_current_user(tenant_id, user_data.dict())
    pass


@router.get("/tenants/{tenant_id}/fetch-users-basic-info", response_model=List[UserBasicInfoResponse])
async def fetch_users_basic_info(tenant_id: UUID, user_service: UserService = Depends(get_user_service)) -> list[
    UserBasicInfoResponse]:
    """Fetch basic info for a list of users"""
    users = await user_service.get_users_basic_info(tenant_id)
    return [
        UserBasicInfoResponse(
            id=user.id,
            name=user.name,
            email=user.email,
            tenant_id=user.tenant_id,
            organization_id=user.organization_id,
        )
        for user in users
    ]
