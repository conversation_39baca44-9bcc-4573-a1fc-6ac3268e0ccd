from typing import List
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException

from app.api.schemas.common import SuccessResponse, ErrorCode
from app.api.schemas.legal_entity import (
    LegalEntityCreate,
    LegalEntityUpdate,
    LegalEntityResponse,
)
from app.core.models.legal_entity import LegalEntity
from app.core.services.legal_entity_service import LegalEntityService
from app.api.dependencies import get_legal_entity_service
from app.api.decorators.auth_decorators import require_privileges

router = APIRouter(prefix="/tenants/{tenant_id}/legal-entities", tags=["Legal Entity Management"])


@router.post("", response_model=SuccessResponse[LegalEntityResponse])
@require_privileges(["CREATE_LEGAL_ENTITY"], validate_tenant=True)
async def create_legal_entity(
    tenant_id: UUID,
    legal_entity: LegalEntityCreate,
    service: LegalEntityService = Depends(get_legal_entity_service),
) -> SuccessResponse[LegalEntityResponse]:
    """Create a new legal entity for a tenant."""
    try:
        created_entity = await service.create_legal_entity(
            tenant_id=tenant_id,
            legal_entity_data=LegalEntity(
                name=legal_entity.name,
                tenant_id=tenant_id,
                legal_name=legal_entity.legal_name,
                legal_entity_type=legal_entity.legal_entity_type,
                address=legal_entity.address,
                incorporation_date=legal_entity.incorporation_date,
                is_default=legal_entity.is_default,
                registration_number=legal_entity.registration_number,
                tax_identifier=legal_entity.tax_identifier,
                jurisdiction_country=legal_entity.jurisdiction_country,
                functional_currency=legal_entity.functional_currency,
            ),
        )
        return SuccessResponse(
            data=LegalEntityResponse.from_model(created_entity),
            message="Legal entity created successfully",
        )
    except ValueError as e:
        raise HTTPException(
            status_code=400,
            detail={
                "code": ErrorCode.INVALID_REQUEST,
                "message": str(e),
            },
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "code": ErrorCode.INTERNAL_ERROR,
                "message": "Failed to create legal entity",
                "details": str(e),
            },
        )


@router.get("/{id}", response_model=SuccessResponse[LegalEntityResponse])
async def get_legal_entity(
    tenant_id: UUID,
    id: UUID,
    service: LegalEntityService = Depends(get_legal_entity_service),
) -> SuccessResponse[LegalEntityResponse]:
    """Get a legal entity by ID for a tenant."""
    try:
        legal_entity = await service.get_legal_entity_by_id(tenant_id=tenant_id, id=id)
        if not legal_entity:
            raise HTTPException(
                status_code=404,
                detail={
                    "code": ErrorCode.NOT_FOUND,
                    "message": "Legal entity not found",
                },
            )
        return SuccessResponse(
            data=LegalEntityResponse.from_model(legal_entity),
            message="Legal entity retrieved successfully",
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "code": ErrorCode.INTERNAL_ERROR,
                "message": "Failed to retrieve legal entity",
                "details": str(e),
            },
        )


@router.get("", response_model=SuccessResponse[List[LegalEntityResponse]])
async def get_all_legal_entities(
    tenant_id: UUID,
    service: LegalEntityService = Depends(get_legal_entity_service),
) -> SuccessResponse[List[LegalEntityResponse]]:
    """Get all legal entities for a tenant with pagination."""
    try:
        legal_entities = await service.get_all_legal_entities(tenant_id)
        return SuccessResponse(
            data=[LegalEntityResponse.from_model(entity) for entity in legal_entities],
            message="Legal entities retrieved successfully",
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "code": ErrorCode.INTERNAL_ERROR,
                "message": "Failed to retrieve legal entities",
                "details": str(e),
            },
        )


@router.put("/{id}", response_model=SuccessResponse[LegalEntityResponse])
@require_privileges(["UPDATE_LEGAL_ENTITY"], validate_tenant=True)
async def update_legal_entity(
    tenant_id: UUID,
    id: UUID,
    legal_entity_update: LegalEntityUpdate,
    service: LegalEntityService = Depends(get_legal_entity_service),
) -> SuccessResponse[LegalEntityResponse]:
    """Update a legal entity for a tenant."""
    try:
        legal_entity = await service.get_legal_entity_by_id(id)
        if not legal_entity or legal_entity.tenant_id != tenant_id:
            raise HTTPException(
                status_code=404,
                detail={
                    "code": ErrorCode.NOT_FOUND,
                    "message": "Legal entity not found",
                },
            )

        if legal_entity_update.name:
            legal_entity.name = legal_entity_update.name
        if legal_entity_update.legal_entity_type:
            legal_entity.legal_entity_type = legal_entity_update.legal_entity_type
        if legal_entity_update.address:
            legal_entity.address = legal_entity_update.address
        if legal_entity_update.legal_name:
            legal_entity.legal_name = legal_entity_update.legal_name
        if legal_entity_update.incorporation_date:
            legal_entity.incorporation_date = legal_entity_update.incorporation_date
        if legal_entity_update.is_default:
            legal_entity.is_default = legal_entity_update.is_default
        if legal_entity_update.registration_number:
            legal_entity.registration_number = legal_entity_update.registration_number
        if legal_entity_update.tax_identifier:
            legal_entity.tax_identifier = legal_entity_update.tax_identifier

        updated_entity = await service.update_legal_entity(legal_entity)
        return SuccessResponse(
            data=LegalEntityResponse.from_model(updated_entity),
            message="Legal entity updated successfully",
        )
    except ValueError as e:
        raise HTTPException(
            status_code=400,
            detail={
                "code": ErrorCode.INVALID_REQUEST,
                "message": str(e),
            },
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "code": ErrorCode.INTERNAL_ERROR,
                "message": "Failed to update legal entity",
                "details": str(e),
            },
        )
