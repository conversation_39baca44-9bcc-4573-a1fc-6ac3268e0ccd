from typing import Any, Dict

from fastapi import APIRouter, HTTPException, status

from app.config.app_config import app_config

from app.api.dependencies import AuthServiceDep
from app.api.schemas.auth import (
    LoginRequest,
    PasswordUpdateRequest,
    RefreshTokenRequest,
    SocialLoginRequest,
    SuccessResponse,
    TokenResponse,
    RefreshTokenResponse,
    TokenValidationRequest,
)
from app.api.schemas.common import ErrorCode
from fastapi import Request, Response


router = APIRouter(prefix="/auth", tags=["Authentication"])


@router.post("/login", response_model=SuccessResponse[TokenResponse])
async def login(request: LoginRequest, auth_service: AuthServiceDep) -> Dict[str, Any]:
    """Email/password login"""
    try:
        token_data = await auth_service.login(request.email, request.password)
        return SuccessResponse(success=True, data=token_data, message="Login successful")
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail={"code": ErrorCode.UNAUTHORIZED, "message": str(e)}
        )


@router.post("/social/{provider}/login", response_model=SuccessResponse[TokenResponse])
async def social_login(provider: str, request: SocialLoginRequest, auth_service: AuthServiceDep) -> Dict[str, Any]:
    """Social login"""
    try:
        token_data = await auth_service.social_login(provider, request.token)
        return SuccessResponse(success=True, data=token_data, message="Social login successful")
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail={"code": ErrorCode.UNAUTHORIZED, "message": str(e)}
        )


@router.post("/refresh", response_model=SuccessResponse[RefreshTokenResponse])
async def refresh_token(request: RefreshTokenRequest, auth_service: AuthServiceDep) -> Dict[str, Any]:
    """Refresh access token"""
    try:
        token_data = await auth_service.refresh_token(request.refresh_token)
        return SuccessResponse(success=True, data=token_data, message="Token refreshed successfully")
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail={"code": ErrorCode.UNAUTHORIZED, "message": str(e)}
        )


@router.post("/password/update", response_model=SuccessResponse[dict])
async def update_password(request: PasswordUpdateRequest, auth_service: AuthServiceDep) -> Dict[str, Any]:
    """Update password using reset token"""
    try:
        await auth_service.update_password(request.token, request.new_password)
        return SuccessResponse(success=True, data={}, message="Password updated successfully")
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail={"code": ErrorCode.VALIDATION_ERROR, "message": str(e)}
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"code": ErrorCode.INTERNAL_ERROR, "message": str(e)},
        )


@router.post("/validate-token", response_model=SuccessResponse[dict])
async def validate_token(
    request: TokenValidationRequest, response: Response, auth_service: AuthServiceDep
) -> Dict[str, Any]:
    """Validate JWT token"""
    return await __validate_token(request.token, response, auth_service)


@router.get("/validate-token", response_model=SuccessResponse[dict])
async def validate_token_get(request: Request, response: Response, auth_service: AuthServiceDep) -> Dict[str, Any]:
    """Validate JWT token"""
    return await __validate_token(request.headers.get("Authorization"), response, auth_service)


async def __validate_token(token: str, response: Response, auth_service: AuthServiceDep) -> Dict[str, Any]:
    try:
        if not token:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail={"code": ErrorCode.UNAUTHORIZED, "message": "Token is required"},
            )
        
        # To check if the token is a Supabase generated token
        is_supabase_generated_token = await auth_service.is_supabase_token(token)

        if is_supabase_generated_token:
            payload = await auth_service.validate_supabase_auth_token(token)
        else:
            payload = await auth_service.validate_token(token)
            
        # if the payload is valid, set few headers in the response object
        response.headers["x-user-id"] = payload.get("sub")

        if payload.get("tenant_id"):
            response.headers["x-tenant-id"] = payload.get("tenant_id")
        else:
            response.headers["x-tenant-id"] = app_config.DEFAULT_TENANT_ID
        
        if payload.get("organization_id"):
            response.headers["x-organization-id"] = payload.get("organization_id")

        return SuccessResponse(success=True, data=payload, message="Token is valid")
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail={"code": ErrorCode.UNAUTHORIZED, "message": str(e)}
        )
    except HTTPException as e:
        raise e
    except Exception as e:
        raise e
