from typing import Optional, List
from pydantic import BaseModel, EmailStr
from uuid import UUID

from app.api.schemas.common import SuccessResponse


class LoginRequest(BaseModel):
    email: EmailStr
    password: str
    tenant_id: Optional[UUID] = None


class TokenData(BaseModel):
    email: Optional[str] = None
    tenant_id: Optional[str] = None


class PasswordResetRequest(BaseModel):
    email: EmailStr


class PasswordResetConfirm(BaseModel):
    token: str
    new_password: str


class ChangePasswordRequest(BaseModel):
    current_password: str
    new_password: str


class RefreshTokenRequest(BaseModel):
    refresh_token: str


class TokenResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    name: str
    email: Optional[str] = None
    user_id: Optional[UUID] = None
    tenant_id: UUID
    organization_id: UUID
    tenant_region_url: str


class SocialLoginRequest(BaseModel):
    token: str


class RefreshTokenResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"


class PasswordUpdateRequest(BaseModel):
    token: str
    new_password: str


class TokenValidationRequest(BaseModel):
    token: str


class AuthorizationRequest(BaseModel):
    resource: str
    action: str
    tenant_id: Optional[str] = None
    organization_id: Optional[str] = None
    scopes: Optional[List[str]] = None


# Response types
LoginResponse = SuccessResponse[TokenResponse]
RefreshResponse = SuccessResponse[RefreshTokenResponse]
PasswordResetResponse = SuccessResponse[dict]
PasswordUpdateResponse = SuccessResponse[dict]
TokenValidationResponse = SuccessResponse[dict]
AuthorizationResponse = SuccessResponse[dict]
SocialLoginResponse = SuccessResponse[TokenResponse]
