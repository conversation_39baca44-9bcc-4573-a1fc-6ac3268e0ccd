from datetime import datetime
from typing import Optional, List
from uuid import UUID
from pydantic import BaseModel, EmailStr
from .common import SuccessResponse, ErrorResponse
from .role import RoleResponse


class UserBase(BaseModel):
    """Base schema for user data"""

    email: EmailStr
    first_name: str
    last_name: str


class UserCreate(UserBase):
    """Schema for creating a user"""

    password: str
    tenant_id: UUID
    organization_id: UUID

    class Config:
        extra = "ignore"


class UserUpdate(BaseModel):
    """Schema for updating a user"""

    password: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    active: Optional[bool] = None

    class Config:
        extra = "ignore"  # This will ignore any extra fields in the request
        from_attributes = True


class UserResponse(UserBase):
    """Schema for user response"""

    id: UUID
    tenant_id: UUID
    organization_id: UUID
    email: EmailStr
    first_name: str
    last_name: str
    active: bool
    created_at: datetime
    updated_at: datetime
    roles: List[RoleResponse] = []

    class Config:
        from_attributes = True


# Response type aliases
GetUserResponse = SuccessResponse[UserResponse]
ListUsersResponse = SuccessResponse[List[UserResponse]]


class UserProfileUpdate(BaseModel):
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    phone: Optional[str] = None
    avatar_url: Optional[str] = None

    class Config:
        extra = "ignore"


class UserPasswordUpdate(BaseModel):
    current_password: str
    new_password: str


class UserLogin(BaseModel):
    email: EmailStr
    password: str


class Token(BaseModel):
    access_token: str
    token_type: str = "bearer"


class TokenData(BaseModel):
    user_id: str
    tenant_id: str
    roles: List[str] = []


class UserRoleAssign(BaseModel):
    role_ids: List[str]


class UserBasicInfoResponse(BaseModel):
    id: UUID
    name: str
    email: str
    tenant_id: UUID
    organization_id: UUID

